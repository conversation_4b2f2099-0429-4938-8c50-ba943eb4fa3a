import os
import re
import subprocess
import torch

def run_command(command):
    """执行命令并返回其输出。"""
    print(f"正在运行命令: {command}")
    try:
        process = subprocess.Popen(command, shell=True, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True, encoding='utf-8')
        output_lines = []
        while True:
            line = process.stdout.readline()
            if not line:
                break
            print(line.strip())
            output_lines.append(line.strip())
        process.wait()
        if process.returncode != 0:
            print(f"命令执行失败，返回码: {process.returncode}")
        return "\n".join(output_lines)
    except Exception as e:
        print(f"执行命令时出错: {e}")
        return None

def parse_scores(output):
    """从 topk.py 的输出中解析 macro 和 micro 分数。"""
    macro_match = re.search(r"'macro':\s*([\d.]+)", output)
    micro_match = re.search(r"'micro':\s*([\d.]+)", output)
    
    macro_score = float(macro_match.group(1)) if macro_match else 0.0
    micro_score = float(micro_match.group(1)) if micro_match else 0.0
    
    return macro_score, micro_score

def main():
    """主函数，用于协调训练和评估过程。"""
    # 确保 'ckpts' 和 'pkl_files' 目录存在
    os.makedirs("ckpts", exist_ok=True)
    os.makedirs("pkl_files", exist_ok=True)

    # 定义文件路径
    ckpt_path = os.path.join("ckpts", "model.ckpt")
    pkl_path = os.path.join("pkl_files", "embedding.pkl")

    # 初始化最佳分数和早停计数器
    best_macro_score = 0.0
    best_micro_score = 0.0
    early_stop_patience = 10  # 如果连续10轮没有提升，则停止
    early_stop_counter = 0

    # 训练循环
    for epoch in range(1, 101):  # 最多训练100轮
        print(f"--- 第 {epoch} 轮 ---")

        # 1. 运行 train.py
        # 注意：我们假设 train.py 能够通过 --save_path 参数接收模型保存路径
        # 并且 --epochs 1 表示只训练一轮
        train_command = f"python train.py --max_epochs 1 --save_path {ckpt_path}"
        if epoch > 1:
            train_command += f" --load_path {ckpt_path}" # 从上一轮的ckpt加载
        run_command(train_command)

        # 2. 运行 embedding.py
        # 注意：我们假设 embedding.py 能够通过 --load_state_dict_path 加载模型
        # 并且通过 --output_pkl_path 指定输出的 pkl 文件路径
        embedding_command = f"python embedding.py --load_state_dict_path {ckpt_path} --output_pkl_path {pkl_path}"
        run_command(embedding_command)

        # 3. 运行 topk.py
        # 注意：我们假设 topk.py 能够通过 --load_state_dict_path 加载模型
        # 并且通过 --embedding_path 加载 pkl 文件
        topk_command = f"python topk.py --load_state_dict_path {ckpt_path} --embedding_path {pkl_path}"
        topk_output = run_command(topk_command)

        # 4. 解析分数并检查是否早停
        if topk_output:
            macro_score, micro_score = parse_scores(topk_output)
            print(f"第 {epoch} 轮 - Macro: {macro_score:.4f}, Micro: {micro_score:.4f}")

            if macro_score > best_macro_score:
                best_macro_score = macro_score
                best_micro_score = micro_score
                early_stop_counter = 0
                print(f"发现新的最佳分数！保存当前模型。")
                # 模型已经在train.py中保存了，这里可以根据需要添加其他操作
            else:
                early_stop_counter += 1
                print(f"分数未提升。早停计数: {early_stop_counter}/{early_stop_patience}")

            if early_stop_counter >= early_stop_patience:
                print(f"连续 {early_stop_patience} 轮分数未提升，触发早停。")
                break
        else:
            print("无法获取 topk.py 的输出，终止训练。")
            break
    
    print("\n--- 训练结束 ---")
    print(f"最佳 Macro 分数: {best_macro_score:.4f}")
    print(f"最佳 Micro 分数: {best_micro_score:.4f}")

if __name__ == "__main__":
    main()
