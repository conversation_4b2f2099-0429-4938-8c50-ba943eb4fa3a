#!/usr/bin/env python3
"""
Test script to verify batch sizes and dataloader lengths
"""

import os
import torch
import argparse
from openprompt.utils.reproduciblity import set_seed
from openprompt.prompts import SoftVerbalizer, ManualTemplate

from processor import PROCESSOR
from util.utils import load_plm_from_config, print_info
from util.data_loader import SinglePathPromptDataLoader


def test_dataloader_lengths():
    """Test dataloader lengths with different batch sizes"""
    
    # Setup basic args
    args = argparse.Namespace()
    args.dataset = "wos"
    args.shot = 30
    args.seed = 171
    args.model = 'bert'
    args.model_name_or_path = '/home/<USER>/ZhouSQ/DCX/TACL_chinese1/chinese-roberta-wwm-ext'
    args.max_seq_lens = 512
    args.multi_mask = 1
    args.template_id = 0
    args.use_multi_gpu = False
    args.device = 1
    
    # Setup device
    if args.device != -1:
        os.environ["CUDA_VISIBLE_DEVICES"] = f"{args.device}"
        device = torch.device("cuda:0")
    else:
        device = torch.device("cpu")
    
    set_seed(args.seed)
    
    # Setup processor and dataset
    processor = PROCESSOR[args.dataset](shot=args.shot, seed=args.seed)
    args.depth = len(processor.hier_mapping) + 1
    dataset = {
        'train': processor.train_example,
        'dev': processor.dev_example,
        'test': processor.test_example
    }
    
    print_info(f"Test dataset size: {len(dataset['test'])}")
    
    # Setup model components
    plm, tokenizer, model_config, WrapperClass = load_plm_from_config(args, args.model_name_or_path)
    
    # Setup template
    if args.multi_mask:
        template_file = f"{args.dataset}_mask_template.txt"
    else:
        template_file = "manual_template.txt"
        
    template_path = "template"
    text_mask = []
    for i in range(args.depth):
        text_mask.append(f'{i + 1} level: {{"mask"}}')
    text = f'It was {" ".join(text_mask)}. {{"placeholder": "text_a"}}'
    
    if not os.path.exists(template_path):
        os.mkdir(template_path)
        
    template_path = os.path.join(template_path, template_file)
    if not os.path.exists(template_path):
        with open(template_path, 'w', encoding='utf-8') as fp:
            fp.write(text)
            
    template = ManualTemplate(tokenizer=tokenizer).from_file(template_path, choice=args.template_id)
    
    # Test different batch sizes
    batch_sizes = [8, 16, 32]
    
    for batch_size in batch_sizes:
        print_info(f"\n=== Testing batch_size = {batch_size} ===")
        
        # Create dataloader
        test_dataloader = SinglePathPromptDataLoader(
            dataset=dataset['test'], 
            template=template, 
            tokenizer=tokenizer,
            tokenizer_wrapper_class=WrapperClass, 
            max_seq_length=args.max_seq_lens,
            decoder_max_length=3,
            batch_size=batch_size,
            shuffle=False, 
            teacher_forcing=False,
            predict_eos_token=False, 
            truncate_method="tail",
            num_works=4,
            multi_gpu=False
        )
        
        print_info(f"Dataloader length: {len(test_dataloader)}")
        print_info(f"Expected batches: {len(dataset['test']) // batch_size + (1 if len(dataset['test']) % batch_size != 0 else 0)}")
        
        # Test first batch
        first_batch = next(iter(test_dataloader))
        print_info(f"First batch shape: {first_batch[0].shape}")
    
    # Test loading existing test_dataloader
    print_info(f"\n=== Testing existing test_dataloader file ===")
    test_path = os.path.join(f"/home/<USER>/ZhouSQ/DCX/TACL_chinese1/DCL/dataset", "WebOfScience", f"test_dataloader-multi_mask.pt")
    
    if os.path.exists(test_path):
        existing_dataloader = torch.load(test_path, weights_only=False)
        print_info(f"Existing dataloader length: {len(existing_dataloader)}")
        
        # Test first batch from existing dataloader
        first_batch = next(iter(existing_dataloader))
        print_info(f"Existing dataloader first batch shape: {first_batch[0].shape}")
        print_info(f"Inferred batch_size from existing dataloader: {first_batch[0].shape[0]}")
    else:
        print_info("Existing test_dataloader file not found")


if __name__ == "__main__":
    test_dataloader_lengths()
