# 集成训练脚本使用说明

## 概述

`integrated_training.py` 是一个集成了 `train.py`、`embedding.py` 和 `topk.py` 功能的完整训练流程脚本。它实现了以下功能：

1. **训练阶段**：使用 `hierVerb.py` 模型进行训练
2. **嵌入生成**：使用 `embedding_chy.py` 模型生成文档嵌入
3. **TopK评估**：使用 `topk_chy.py` 模型进行相似性评估
4. **早停机制**：基于 macro 和 micro F1 分数的组合进行早停

## 主要特性

- **自动化流程**：每个训练轮次后自动生成嵌入和评估
- **早停机制**：当性能不再提升时自动停止训练
- **TensorBoard日志**：完整的训练过程可视化
- **多GPU支持**：支持单GPU和多GPU训练
- **结果保存**：自动保存最佳模型和结果

## 使用方法

### 基本使用

```bash
python integrated_training.py
```

### 自定义参数

```bash
python integrated_training.py \
    --dataset wos \
    --batch_size 64 \
    --lr 3e-5 \
    --max_epochs 50 \
    --early_stop 15 \
    --topk 3 \
    --seed 171
```

### 多GPU训练

```bash
python integrated_training.py \
    --use_multi_gpu True \
    --batch_size 64
```

## 主要参数说明

### 模型参数
- `--model`: 模型类型 (默认: 'bert')
- `--model_name_or_path`: 预训练模型路径
- `--dataset`: 数据集名称 (默认: 'wos')
- `--depth`: 层次深度 (默认: 7)

### 训练参数
- `--lr`: 学习率 (默认: 3e-5)
- `--lr2`: 第二个优化器学习率 (默认: 1e-4)
- `--batch_size`: 批次大小 (默认: 64)
- `--max_epochs`: 最大训练轮数 (默认: 50)
- `--early_stop`: 早停轮数 (默认: 15)

### 损失函数参数
- `--lm_training`: 是否使用语言模型训练 (默认: 1)
- `--lm_alpha`: 语言模型损失权重 (默认: 0.7)
- `--contrastive_loss`: 是否使用对比损失 (默认: 0)
- `--contrastive_alpha`: 对比损失权重 (默认: 0.9)

### 评估参数
- `--topk`: TopK相似性评估的K值 (默认: 3)
- `--eval_mode`: 评估模式 (默认: 0)

### 设备参数
- `--device`: GPU设备号 (默认: 1)
- `--use_multi_gpu`: 是否使用多GPU (默认: True)
- `--use_fp16`: 是否使用FP16精度 (默认: False)

## 输出文件

### 模型文件
- `ckpts/{run_id}-macro.ckpt`: 最佳macro F1模型权重
- `_{shot}shot_none_{seed}_embed_doc_{label_description}.pkl`: 文档嵌入文件

### 结果文件
- `result/best_results_{run_id}.txt`: 最佳结果记录
- `result/integrated_train.txt`: 完整训练日志
- `runs/integrated_train/{run_id}/`: TensorBoard日志

### Excel结果文件
- `result/confusion_matrix_test_seed171_0710_12.xlsx`: 混淆矩阵和详细评估结果

## 训练流程

1. **初始化**：
   - 设置设备和环境
   - 加载数据和模型
   - 创建数据加载器

2. **训练循环**：
   - 训练一个epoch
   - 在验证集上评估
   - 如果性能提升：
     - 保存模型权重
     - 生成文档嵌入
     - 进行TopK评估
     - 更新最佳分数
   - 如果性能未提升：
     - 增加早停计数

3. **早停检查**：
   - 如果连续 `early_stop` 轮未提升则停止训练

4. **最终评估**：
   - 使用最佳模型进行最终评估
   - 保存完整结果

## 监控训练过程

### TensorBoard
```bash
tensorboard --logdir runs/integrated_train/
```

### 日志文件
训练过程中的详细日志会保存在控制台输出和结果文件中。

## 注意事项

1. **内存需求**：确保有足够的GPU内存进行训练和嵌入生成
2. **存储空间**：嵌入文件可能较大，确保有足够存储空间
3. **路径设置**：确保所有数据路径正确设置
4. **依赖检查**：确保所有必要的Python包已安装

## 故障排除

### 常见问题

1. **CUDA内存不足**：
   - 减小 `batch_size`
   - 使用 `--use_fp16 True`

2. **文件路径错误**：
   - 检查 `model_name_or_path` 是否正确
   - 确保数据文件存在

3. **多GPU问题**：
   - 设置 `--use_multi_gpu False` 使用单GPU
   - 检查CUDA_VISIBLE_DEVICES环境变量

## 性能优化建议

1. **批次大小**：根据GPU内存调整批次大小
2. **学习率**：可以尝试不同的学习率组合
3. **早停轮数**：根据数据集大小调整早停轮数
4. **TopK值**：根据任务需求调整TopK值

## 扩展功能

脚本支持原始代码的所有功能，包括：
- 多标签分类
- 层次约束损失
- 对比学习
- 不平衡权重处理
- 多种评估模式

通过修改相应参数即可启用这些功能。
